#!/usr/bin/env python3
"""
Demonstration of enhanced GTO explanations.
Shows how the detailed explanations work for various poker scenarios.
"""

# Import the enhanced explanation functions
from enhanced_gto_explanations import *

# Mock card class for demonstration
class MockCard:
    def __init__(self, rank, suit, value):
        self.rank = rank
        self.suit = suit
        self.value = value

# Mock positions dictionary
POSITIONS = {
    'SB': {'name': 'Small Blind', 'aggression': 0.3, 'fold_threshold': 0.4},
    'BB': {'name': 'Big Blind', 'aggression': 0.4, 'fold_threshold': 0.35},
    'EP': {'name': 'Early Position', 'aggression': 0.2, 'fold_threshold': 0.5},
    'MP': {'name': 'Middle Position', 'aggression': 0.35, 'fold_threshold': 0.45},
    'LP': {'name': 'Late Position', 'aggression': 0.5, 'fold_threshold': 0.3}
}

def demo_preflop_explanations():
    """Demonstrate enhanced preflop explanations"""
    print("🃏 DEMO: Enhanced Preflop GTO Explanations")
    print("=" * 60)
    
    # Scenario 1: Premium pair in late position
    print("\n📋 Scenario 1: Pocket Kings in Late Position")
    hole_cards = [MockCard('K', '♠', 11), MockCard('K', '♥', 11)]
    explain_preflop_gto_enhanced(hole_cards, "RAISE", "LP", "CHECK", POSITIONS)
    
    # Scenario 2: Suited connectors in early position
    print("\n" + "-" * 60)
    print("\n📋 Scenario 2: 78 suited in Early Position")
    hole_cards = [MockCard('7', '♠', 5), MockCard('8', '♠', 6)]
    explain_preflop_gto_enhanced(hole_cards, "FOLD", "EP", "RAISE", POSITIONS)
    
    # Scenario 3: Ace-King offsuit in middle position
    print("\n" + "-" * 60)
    print("\n📋 Scenario 3: AK offsuit in Middle Position")
    hole_cards = [MockCard('A', '♠', 12), MockCard('K', '♥', 11)]
    explain_preflop_gto_enhanced(hole_cards, "RAISE", "MP", "CHECK", POSITIONS)

def demo_postflop_explanations():
    """Demonstrate enhanced postflop explanations"""
    print("\n\n🃏 DEMO: Enhanced Postflop GTO Explanations")
    print("=" * 60)
    
    # Scenario 1: Strong hand on dry board
    print("\n📋 Scenario 1: Top Pair on Dry Board")
    hole_cards = [MockCard('A', '♠', 12), MockCard('K', '♥', 11)]
    community_cards = [MockCard('A', '♥', 12), MockCard('7', '♦', 5), MockCard('2', '♣', 0)]
    
    # Mock the hand evaluation functions
    def mock_evaluate_hand(cards):
        return cards[:5], 1500000  # One pair
    
    def mock_get_hand_name(cards):
        return "One Pair"
    
    globals()['evaluate_hand'] = mock_evaluate_hand
    globals()['get_hand_name'] = mock_get_hand_name
    
    explain_postflop_gto_enhanced(
        hole_cards, community_cards, "BET", "LP", "CHECK", 
        0.75, 100, 0, "flop", POSITIONS
    )
    
    # Scenario 2: Draw on wet board
    print("\n" + "-" * 60)
    print("\n📋 Scenario 2: Flush Draw on Wet Board")
    hole_cards = [MockCard('9', '♦', 7), MockCard('8', '♦', 6)]
    community_cards = [MockCard('A', '♦', 12), MockCard('7', '♦', 5), MockCard('K', '♠', 11)]
    
    explain_postflop_gto_enhanced(
        hole_cards, community_cards, "CALL", "MP", "BET", 
        0.35, 100, 50, "flop", POSITIONS
    )
    
    # Scenario 3: Weak hand facing big bet
    print("\n" + "-" * 60)
    print("\n📋 Scenario 3: Weak Hand Facing Large Bet")
    hole_cards = [MockCard('6', '♠', 4), MockCard('5', '♥', 3)]
    community_cards = [MockCard('A', '♦', 12), MockCard('K', '♦', 11), MockCard('Q', '♠', 10), MockCard('J', '♣', 9)]
    
    explain_postflop_gto_enhanced(
        hole_cards, community_cards, "FOLD", "EP", "BET", 
        0.15, 200, 150, "turn", POSITIONS
    )

def demo_board_analysis():
    """Demonstrate board texture analysis"""
    print("\n\n🃏 DEMO: Board Texture Analysis")
    print("=" * 60)
    
    # Different board textures
    boards = [
        ([MockCard('A', '♠', 12), MockCard('7', '♥', 5), MockCard('2', '♣', 0)], "Dry rainbow board"),
        ([MockCard('9', '♦', 7), MockCard('8', '♦', 6), MockCard('7', '♠', 5)], "Coordinated board"),
        ([MockCard('A', '♦', 12), MockCard('K', '♦', 11), MockCard('Q', '♦', 10)], "Wet flush board"),
        ([MockCard('8', '♠', 6), MockCard('8', '♥', 6), MockCard('3', '♣', 1)], "Paired board")
    ]
    
    for board, description in boards:
        texture = analyze_board_texture(board)
        print(f"\n📋 {description}:")
        print(f"Cards: {', '.join([f'{card.rank}{card.suit}' for card in board])}")
        print(f"Texture: {texture}")

def main():
    """Run all demonstrations"""
    print("🎯 Enhanced GTO Explanations - Live Demo")
    print("=" * 60)
    print("This demonstrates how the enhanced explanations work.")
    print("They provide detailed context for every GTO decision!")
    
    demo_preflop_explanations()
    demo_postflop_explanations()
    demo_board_analysis()
    
    print("\n\n" + "=" * 60)
    print("🏆 ENHANCED EXPLANATIONS SUMMARY")
    print("=" * 60)
    print("✅ Detailed reasoning for every GTO recommendation")
    print("✅ Hand strength assessment in context")
    print("✅ Position-based strategic advice")
    print("✅ Board texture analysis")
    print("✅ Opponent action interpretation")
    print("✅ Mathematical justification (equity vs pot odds)")
    print("✅ Stage-specific considerations")
    print("\n🎯 Players now understand WHY each decision is optimal!")
    print("📚 This transforms the trainer into a comprehensive poker education tool!")

if __name__ == "__main__":
    main()

import random
import itertools
from collections import Counter
from IPython.display import display, HTML, clear_output
import ipywidgets as widgets
from ipywidgets import interact, interactive, fixed, interact_manual

# Card representation
SUITS = ['♠', '♥', '♦', '♣']
RANKS = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A']
RANK_VALUES = {rank: i for i, rank in enumerate(RANKS)}

class Card:
    def __init__(self, rank, suit):
        self.rank = rank
        self.suit = suit
        self.value = RANK_VALUES[rank]
    
    def __str__(self):
        return f"{self.rank}{self.suit}"
    
    def __repr__(self):
        return self.__str__()
    
    def display_html(self):
        color = 'red' if self.suit in ['♥', '♦'] else 'black'
        return f'<span style="color: {color}; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;">{self.rank}{self.suit}</span>'

class Deck:
    def __init__(self):
        self.cards = [Card(rank, suit) for suit in SUITS for rank in RANKS]
        self.shuffle()
    
    def shuffle(self):
        random.shuffle(self.cards)
    
    def deal(self, num_cards=1):
        return [self.cards.pop() for _ in range(num_cards)]

# Hand evaluation functions
def evaluate_hand(cards):
    """Evaluate a 5-7 card poker hand and return the best 5-card hand"""
    if len(cards) < 5:
        return None, 0
    
    best_hand = None
    best_rank = 0
    
    # Try all combinations of 5 cards
    for combo in itertools.combinations(cards, 5):
        hand_rank = get_hand_rank(list(combo))
        if hand_rank > best_rank:
            best_rank = hand_rank
            best_hand = list(combo)
    
    return best_hand, best_rank

def get_hand_rank(cards):
    """Get numerical rank of a 5-card hand (higher = better)"""
    ranks = sorted([card.value for card in cards], reverse=True)
    suits = [card.suit for card in cards]
    
    is_flush = len(set(suits)) == 1
    is_straight = ranks == list(range(ranks[0], ranks[0] - 5, -1))
    
    # Special case for A-2-3-4-5 straight
    if ranks == [12, 3, 2, 1, 0]:  # A, 5, 4, 3, 2
        is_straight = True
        ranks = [3, 2, 1, 0, 12]  # Treat ace as low
    
    rank_counts = Counter(ranks)
    counts = sorted(rank_counts.values(), reverse=True)
    
    # Hand rankings (higher number = better hand)
    if is_straight and is_flush:
        return 8000000 + ranks[0]  # Straight flush
    elif counts == [4, 1]:
        return 7000000 + max(rank_counts, key=rank_counts.get) * 1000 + min(rank_counts, key=rank_counts.get)  # Four of a kind
    elif counts == [3, 2]:
        trips = max(rank_counts, key=rank_counts.get)
        pair = min(rank_counts, key=rank_counts.get)
        return 6000000 + trips * 1000 + pair  # Full house
    elif is_flush:
        return 5000000 + sum(rank * (13 ** i) for i, rank in enumerate(ranks))  # Flush
    elif is_straight:
        return 4000000 + ranks[0]  # Straight
    elif counts == [3, 1, 1]:
        trips = max(rank_counts, key=rank_counts.get)
        kickers = sorted([r for r in ranks if r != trips], reverse=True)
        return 3000000 + trips * 10000 + kickers[0] * 100 + kickers[1]  # Three of a kind
    elif counts == [2, 2, 1]:
        pairs = sorted([r for r, c in rank_counts.items() if c == 2], reverse=True)
        kicker = [r for r, c in rank_counts.items() if c == 1][0]
        return 2000000 + pairs[0] * 10000 + pairs[1] * 100 + kicker  # Two pair
    elif counts == [2, 1, 1, 1]:
        pair = [r for r, c in rank_counts.items() if c == 2][0]
        kickers = sorted([r for r in ranks if r != pair], reverse=True)
        return 1000000 + pair * 100000 + sum(k * (13 ** i) for i, k in enumerate(kickers))  # One pair
    else:
        return sum(rank * (13 ** i) for i, rank in enumerate(ranks))  # High card

def get_hand_name(cards):
    """Get the name of a poker hand"""
    if not cards:
        return "No hand"
    
    ranks = sorted([card.value for card in cards], reverse=True)
    suits = [card.suit for card in cards]
    
    is_flush = len(set(suits)) == 1
    is_straight = ranks == list(range(ranks[0], ranks[0] - 5, -1))
    
    # Special case for A-2-3-4-5 straight
    if ranks == [12, 3, 2, 1, 0]:
        is_straight = True
    
    rank_counts = Counter(ranks)
    counts = sorted(rank_counts.values(), reverse=True)
    
    if is_straight and is_flush:
        return "Straight Flush"
    elif counts == [4, 1]:
        return "Four of a Kind"
    elif counts == [3, 2]:
        return "Full House"
    elif is_flush:
        return "Flush"
    elif is_straight:
        return "Straight"
    elif counts == [3, 1, 1]:
        return "Three of a Kind"
    elif counts == [2, 2, 1]:
        return "Two Pair"
    elif counts == [2, 1, 1, 1]:
        return "One Pair"
    else:
        return "High Card"

# Poker odds and equity calculations
def calculate_equity(hole_cards, community_cards, num_simulations=10000):
    """Calculate equity using Monte Carlo simulation"""
    if len(community_cards) == 5:
        # All cards dealt, just evaluate
        best_hand, hand_rank = evaluate_hand(hole_cards + community_cards)
        return 1.0 if hand_rank > 0 else 0.0
    
    wins = 0
    used_cards = set(str(card) for card in hole_cards + community_cards)
    
    for _ in range(num_simulations):
        # Create a deck without used cards
        available_cards = [Card(rank, suit) for suit in SUITS for rank in RANKS 
                          if f"{rank}{suit}" not in used_cards]
        random.shuffle(available_cards)
        
        # Complete the community cards
        cards_needed = 5 - len(community_cards)
        simulated_community = community_cards + available_cards[:cards_needed]
        
        # Simulate opponent with random hole cards
        opponent_cards = available_cards[cards_needed:cards_needed + 2]
        
        # Evaluate both hands
        player_hand, player_rank = evaluate_hand(hole_cards + simulated_community)
        opponent_hand, opponent_rank = evaluate_hand(opponent_cards + simulated_community)
        
        if player_rank > opponent_rank:
            wins += 1
        elif player_rank == opponent_rank:
            wins += 0.5  # Tie
    
    return wins / num_simulations

def get_outs(hole_cards, community_cards):
    """Calculate outs for improving hand"""
    if len(community_cards) >= 5:
        return 0
    
    current_best, current_rank = evaluate_hand(hole_cards + community_cards)
    used_cards = set(str(card) for card in hole_cards + community_cards)
    
    outs = 0
    for rank in RANKS:
        for suit in SUITS:
            if f"{rank}{suit}" not in used_cards:
                test_card = Card(rank, suit)
                test_community = community_cards + [test_card]
                if len(test_community) <= 5:
                    test_hand, test_rank = evaluate_hand(hole_cards + test_community)
                    if test_rank > current_rank:
                        outs += 1
    
    return outs

def pot_odds_decision(pot_size, bet_size, equity):
    """Calculate if a call is profitable based on pot odds"""
    pot_odds = bet_size / (pot_size + bet_size)
    return equity > pot_odds, pot_odds

def gto_action_recommendation(equity, position, pot_size, bet_size, stack_size):
    """Simplified GTO recommendation based on equity and position"""
    pot_odds = bet_size / (pot_size + bet_size) if bet_size > 0 else 0
    
    # Simplified GTO thresholds
    if equity > 0.65:
        return "RAISE" if stack_size > bet_size * 3 else "CALL"
    elif equity > pot_odds + 0.05:  # Small edge for position
        return "CALL"
    elif equity > 0.3 and bet_size == 0:  # Check with decent equity
        return "CHECK"
    elif bet_size == 0 and equity < 0.3:
        return "CHECK"  # Don't bet weak hands
    else:
        return "FOLD"

# Poker game simulation
class PokerGame:
    def __init__(self):
        self.deck = Deck()
        self.hole_cards = []
        self.community_cards = []
        self.pot_size = 100
        self.current_bet = 0
        self.player_stack = 1000
        self.stage = "preflop"  # preflop, flop, turn, river
        self.score = 0
        self.questions_asked = 0
        self.hand_results = []  # Store results for final summary
        self.current_hand_log = []  # Log for current hand
    
    def start_new_hand(self):
        self.deck = Deck()
        self.hole_cards = self.deck.deal(2)
        self.community_cards = []
        self.pot_size = 100
        self.current_bet = 0
        self.stage = "preflop"
        self.current_hand_log = []  # Reset log for new hand
    
    def deal_flop(self):
        if self.stage == "preflop":
            self.community_cards.extend(self.deck.deal(3))
            self.stage = "flop"
    
    def deal_turn(self):
        if self.stage == "flop":
            self.community_cards.extend(self.deck.deal(1))
            self.stage = "turn"
    
    def deal_river(self):
        if self.stage == "turn":
            self.community_cards.extend(self.deck.deal(1))
            self.stage = "river"
    
    def display_table(self):
        """Display the current poker table state"""
        print("\n" + "="*60)
        print(f"POKER TRAINER - {self.stage.upper()} STAGE")
        print("="*60)
        
        # Display hole cards
        hole_html = "<h3>Your Hole Cards:</h3>" + "".join([card.display_html() for card in self.hole_cards])
        display(HTML(hole_html))
        
        # Display community cards
        if self.community_cards:
            community_html = "<h3>Community Cards:</h3>" + "".join([card.display_html() for card in self.community_cards])
            display(HTML(community_html))
        
        # Display current hand strength
        if len(self.community_cards) >= 3:
            best_hand, _ = evaluate_hand(self.hole_cards + self.community_cards)
            hand_name = get_hand_name(best_hand)
            print(f"\nCurrent Best Hand: {hand_name}")
        
        print(f"\nPot Size: ${self.pot_size}")
        print(f"Current Bet: ${self.current_bet}")
        print(f"Your Stack: ${self.player_stack}")
        print(f"Score: {self.score}/{self.questions_asked}")
    
    def get_equity(self):
        return calculate_equity(self.hole_cards, self.community_cards, 5000)
    
    def get_gto_action(self):
        equity = self.get_equity()
        return gto_action_recommendation(equity, "middle", self.pot_size, self.current_bet, self.player_stack)

# Initialize game
game = PokerGame()
print("Poker Trainer initialized! Run the next cell to start playing.")

# Interactive quiz functions
def ask_hand_ranking_question():
    """Ask player to identify the most common hands"""
    clear_output(wait=True)
    game.display_table()
    
    if len(game.community_cards) < 3:
        print("\nNot enough community cards for hand ranking question.")
        return
    
    # Calculate possible hands
    all_cards = game.hole_cards + game.community_cards
    current_hand, _ = evaluate_hand(all_cards)
    current_hand_name = get_hand_name(current_hand)
    
    # Generate options
    hand_types = ["High Card", "One Pair", "Two Pair", "Three of a Kind", 
                  "Straight", "Flush", "Full House", "Four of a Kind", "Straight Flush"]
    
    # Create multiple choice
    options = [current_hand_name]
    while len(options) < 4:
        option = random.choice(hand_types)
        if option not in options:
            options.append(option)
    
    random.shuffle(options)
    correct_index = options.index(current_hand_name)
    
    print("\n🎯 HAND RANKING QUESTION:")
    print("What is your current best hand?")
    for i, option in enumerate(options):
        print(f"{i+1}. {option}")
    
    try:
        answer = int(input("\nEnter your choice (1-4): ")) - 1
        game.questions_asked += 1
        
        correct = answer == correct_index
        if correct:
            print("✅ Correct! Well done!")
            game.score += 1
        else:
            print(f"❌ Incorrect. The correct answer was: {current_hand_name}")
            
        # Show the actual best 5-card hand
        if current_hand:
            hand_html = "<h4>Your best 5-card hand:</h4>" + "".join([card.display_html() for card in current_hand])
            display(HTML(hand_html))
        
        # Store the result for final summary
        game.add_hand_ranking_result(game.stage, current_hand_name, options[answer] if answer >= 0 else "Invalid", correct)
        
    except (ValueError, IndexError):
        print("Invalid input. Please enter a number between 1 and 4.")

def ask_gto_decision_question():
    """Ask player about optimal GTO decision"""
    clear_output(wait=True)
    game.display_table()
    
    # Set up a betting scenario
    scenarios = [
        {"bet": 0, "pot": 100, "description": "No bet to you"},
        {"bet": 25, "pot": 100, "description": "Opponent bets $25 into $100 pot"},
        {"bet": 50, "pot": 100, "description": "Opponent bets $50 into $100 pot"},
        {"bet": 75, "pot": 150, "description": "Opponent bets $75 into $150 pot"}
    ]
    
    scenario = random.choice(scenarios)
    game.current_bet = scenario["bet"]
    game.pot_size = scenario["pot"]
    
    print(f"\n🎯 GTO DECISION QUESTION:")
    print(f"Scenario: {scenario['description']}")
    
    # Calculate equity and GTO recommendation
    equity = game.get_equity()
    gto_action = game.get_gto_action()
    
    print(f"\nYour equity: {equity:.1%}")
    
    if game.current_bet > 0:
        pot_odds = game.current_bet / (game.pot_size + game.current_bet)
        print(f"Pot odds: {pot_odds:.1%}")
    
    # Action options
    if game.current_bet == 0:
        options = ["CHECK", "BET/RAISE"]
    else:
        options = ["FOLD", "CALL", "RAISE"]
    
    print("\nWhat should you do?")
    for i, option in enumerate(options):
        print(f"{i+1}. {option}")
    
    try:
        answer = int(input(f"\nEnter your choice (1-{len(options)}): ")) - 1
        game.questions_asked += 1
        
        player_action = options[answer]
        
        # Check if answer matches GTO recommendation
        correct = False
        if gto_action == "CHECK" and player_action == "CHECK":
            correct = True
        elif gto_action == "RAISE" and player_action in ["BET/RAISE", "RAISE"]:
            correct = True
        elif gto_action == "CALL" and player_action == "CALL":
            correct = True
        elif gto_action == "FOLD" and player_action == "FOLD":
            correct = True
        
        if correct:
            print("✅ Correct! That's the GTO play!")
            game.score += 1
        else:
            print(f"❌ Incorrect. GTO recommendation: {gto_action}")
        
        # Explain the reasoning
        print(f"\n📊 Analysis:")
        print(f"Your equity: {equity:.1%}")
        if game.current_bet > 0:
            pot_odds = game.current_bet / (game.pot_size + game.current_bet)
            print(f"Pot odds required: {pot_odds:.1%}")
            if equity > pot_odds:
                print("✅ You have sufficient equity to call")
            else:
                print("❌ Insufficient equity to call profitably")
        
        # Store the result for final summary
        game.add_decision_result(game.stage, player_action, gto_action, correct, equity)
        
        # Return True if player folded (to end the hand)
        return player_action == "FOLD"
        
    except (ValueError, IndexError):
        print(f"Invalid input. Please enter a number between 1 and {len(options)}.")
        return False

def run_training_session():
    """Run a complete poker training session"""
    print("🃏 Welcome to Poker Trainer!")
    print("This program will test your poker knowledge and GTO decision-making.")
    print("\nInstructions:")
    print("- You'll be shown poker hands at different stages")
    print("- Answer questions about hand rankings and optimal decisions")
    print("- Learn from GTO analysis and improve your game!")
    
    num_hands = 5  # Number of hands to practice
    
    for hand_num in range(1, num_hands + 1):
        print(f"\n{'='*60}")
        print(f"HAND {hand_num} of {num_hands}")
        print(f"{'='*60}")
        
        # Start new hand
        game.start_new_hand()
        
        # Preflop - just show cards
        print("\n📋 PREFLOP:")
        game.display_table()
        input("\nPress Enter to see the flop...")
        
        # Flop
        game.deal_flop()
        print("\n📋 FLOP:")
        ask_hand_ranking_question()
        input("\nPress Enter to continue...")
        
        # Ask GTO decision on flop
        ask_gto_decision_question()
        input("\nPress Enter to see the turn...")
        
        # Turn
        game.deal_turn()
        print("\n📋 TURN:")
        ask_hand_ranking_question()
        input("\nPress Enter to continue...")
        
        # Ask GTO decision on turn
        ask_gto_decision_question()
        input("\nPress Enter to see the river...")
        
        # River
        game.deal_river()
        print("\n📋 RIVER:")
        ask_hand_ranking_question()
        input("\nPress Enter to continue...")
        
        # Final GTO decision
        ask_gto_decision_question()
        
        # Show final results for this hand
        print(f"\n📊 Hand {hand_num} Complete!")
        print(f"Current Score: {game.score}/{game.questions_asked} ({game.score/max(1,game.questions_asked)*100:.1f}%)")
        
        if hand_num < num_hands:
            input("\nPress Enter for next hand...")
    
    # Final results
    print(f"\n{'='*60}")
    print("🏆 TRAINING SESSION COMPLETE!")
    print(f"{'='*60}")
    print(f"Final Score: {game.score}/{game.questions_asked}")
    print(f"Accuracy: {game.score/max(1,game.questions_asked)*100:.1f}%")
    
    if game.score/max(1,game.questions_asked) >= 0.8:
        print("🌟 Excellent work! You have strong poker fundamentals!")
    elif game.score/max(1,game.questions_asked) >= 0.6:
        print("👍 Good job! Keep practicing to improve your game.")
    else:
        print("📚 Keep studying! Practice makes perfect in poker.")
    
    print("\nThanks for training! Run this cell again to practice more.")

# Start the training session
# Run this cell to begin your poker training!
run_training_session()
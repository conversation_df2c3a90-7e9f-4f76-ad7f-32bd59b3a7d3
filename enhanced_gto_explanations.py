#!/usr/bin/env python3
"""
Enhanced GTO explanations patch for the poker trainer.
This adds detailed explanations of why each GTO recommendation is optimal.

To use: Run this in a Jupyter cell after loading the poker trainer:
exec(open('enhanced_gto_explanations.py').read())
apply_enhanced_explanations()
"""

def analyze_board_texture(community_cards):
    """Analyze the texture of the community cards"""
    if len(community_cards) < 3:
        return "preflop"
    
    suits = [card.suit for card in community_cards]
    ranks = [card.value for card in community_cards]
    
    # Check for flush draws
    suit_counts = {}
    for suit in suits:
        suit_counts[suit] = suit_counts.get(suit, 0) + 1
    max_suit_count = max(suit_counts.values())
    
    # Check for straight draws
    sorted_ranks = sorted(set(ranks))
    gaps = []
    for i in range(len(sorted_ranks) - 1):
        gaps.append(sorted_ranks[i+1] - sorted_ranks[i])
    
    # Determine texture
    if max_suit_count >= 3:
        return "wet (flush draw possible)"
    elif len(gaps) > 0 and max(gaps) <= 2 and len(sorted_ranks) >= 3:
        return "coordinated (straight draws)"
    elif len(set(ranks)) == len(ranks):
        return "rainbow (no pairs)"
    else:
        return "dry (paired board)"

def analyze_hand_vs_board(hole_cards, community_cards):
    """Analyze how the player's hand interacts with the board"""
    if len(community_cards) < 3:
        return "preflop hand"
    
    # Use existing hand evaluation if available
    try:
        best_hand, hand_rank = evaluate_hand(hole_cards + community_cards)
        hand_name = get_hand_name(best_hand)
        
        # Add relative strength assessment
        if hand_rank >= 6000000:  # Full house or better
            return f"{hand_name} (very strong)"
        elif hand_rank >= 3000000:  # Three of a kind or better
            return f"{hand_name} (strong)"
        elif hand_rank >= 1000000:  # One pair or better
            return f"{hand_name} (medium)"
        else:
            return f"{hand_name} (weak)"
    except:
        # Fallback analysis
        return "hand analysis unavailable"

def explain_preflop_gto_enhanced(hole_cards, gto_action, position, opponent_action, positions_dict):
    """Enhanced preflop GTO explanation"""
    
    card1, card2 = hole_cards
    position_name = positions_dict[position]['name']
    
    # Hand description and strength assessment
    if card1.value == card2.value:
        hand_type = "pocket pair"
        hand_desc = f"{card1.rank}{card1.rank}"
        if card1.value >= 10:
            hand_strength = "premium"
        elif card1.value >= 7:
            hand_strength = "medium"
        else:
            hand_strength = "small"
    elif card1.suit == card2.suit:
        hand_type = "suited cards"
        hand_desc = f"{card1.rank}{card2.rank}s"
        high_card = max(card1.value, card2.value)
        if high_card >= 12:
            hand_strength = "strong suited"
        elif high_card >= 10:
            hand_strength = "medium suited"
        else:
            hand_strength = "weak suited"
    else:
        hand_type = "offsuit cards"
        hand_desc = f"{card1.rank}{card2.rank}o"
        high_card = max(card1.value, card2.value)
        if high_card >= 12 and min(card1.value, card2.value) >= 10:
            hand_strength = "strong offsuit"
        else:
            hand_strength = "weak offsuit"
    
    print(f"\n📊 Detailed GTO Analysis:")
    print(f"Your Hand: {hand_desc} ({hand_type})")
    print(f"Position: {position_name} (Aggression Factor: {positions_dict[position]['aggression']:.1f})")
    print(f"Opponent Action: {opponent_action}")
    
    # Detailed GTO explanation
    print(f"\n🎯 Why {gto_action} is GTO:")
    
    if gto_action == "RAISE":
        if hand_strength in ["premium", "strong suited", "strong offsuit"]:
            print(f"• {hand_desc} is a {hand_strength} hand that plays well postflop")
            print(f"• From {position_name}, we can raise for value and build the pot")
            if opponent_action == "CHECK":
                print(f"• Opponent's check shows weakness - good spot to take initiative")
            elif opponent_action in ["BET", "RAISE"]:
                print(f"• Our hand is strong enough to 3-bet for value against opponent's aggression")
        else:
            print(f"• Even though {hand_desc} is {hand_strength}, position allows aggressive play")
            print(f"• {position_name} gives us post-flop advantage")
        
        if hand_type == "pocket pair":
            print(f"• Pocket pairs have good implied odds and play well in raised pots")
    
    elif gto_action == "CALL":
        print(f"• {hand_desc} has decent playability but not strong enough to raise")
        if opponent_action in ["BET", "RAISE"]:
            print(f"• Opponent showed strength, but our hand has enough potential to continue")
        print(f"• Calling keeps pot manageable while maintaining position advantage")
        if hand_type == "suited cards":
            print(f"• Suited cards have good implied odds for flush draws")
        elif hand_type == "pocket pair":
            print(f"• Pocket pairs can hit sets (11.8% chance on flop)")
    
    elif gto_action == "FOLD":
        print(f"• {hand_desc} is too weak to profitably continue")
        if opponent_action in ["BET", "RAISE"]:
            print(f"• Opponent's aggression indicates strength - our hand lacks equity")
        print(f"• From {position_name}, we need stronger hands to play profitably")
        if hand_strength == "weak offsuit":
            print(f"• Offsuit hands play poorly postflop and have limited potential")
    
    # Position-specific advice
    if position in ['EP', 'MP']:
        print(f"• Early/Middle position requires tighter ranges due to players behind")
    elif position in ['LP']:
        print(f"• Late position allows wider ranges and more aggressive play")
    elif position in ['SB', 'BB']:
        print(f"• Blind position requires careful consideration of pot odds and position")

def explain_postflop_gto_enhanced(hole_cards, community_cards, gto_action, position, opponent_action, 
                                equity, pot_size, current_bet, stage, positions_dict):
    """Enhanced postflop GTO explanation"""
    
    position_name = positions_dict[position]['name']
    
    # Analyze board texture and hand
    board_texture = analyze_board_texture(community_cards)
    hand_strength = analyze_hand_vs_board(hole_cards, community_cards)
    
    print(f"\n📊 Detailed GTO Analysis:")
    print(f"Stage: {stage.upper()}")
    print(f"Your Equity: {equity:.1%}")
    print(f"Position: {position_name}")
    print(f"Opponent Action: {opponent_action}")
    print(f"Board Texture: {board_texture}")
    print(f"Your Hand: {hand_strength}")
    
    # Calculate pot odds if facing a bet
    if current_bet > 0:
        pot_odds = current_bet / (pot_size + current_bet)
        print(f"Pot Odds: {pot_odds:.1%}")
    
    print(f"\n🎯 Why {gto_action} is GTO:")
    
    if gto_action == "BET":
        if equity > 0.65:
            print(f"• Strong equity ({equity:.1%}) justifies betting for value")
            print(f"• We want to build the pot with our strong hand")
        elif equity > 0.45:
            print(f"• Medium equity ({equity:.1%}) allows for a bluff/semi-bluff")
            print(f"• Betting can fold out opponent's weaker hands")
        
        if opponent_action == "CHECK":
            print(f"• Opponent's check shows weakness - good spot to bet")
        
        if "dry" in board_texture or "rainbow" in board_texture:
            print(f"• {board_texture.title()} board favors the aggressor")
        elif "wet" in board_texture or "coordinated" in board_texture:
            print(f"• {board_texture.title()} board requires careful betting")
    
    elif gto_action == "CHECK":
        if equity < 0.45:
            print(f"• Low equity ({equity:.1%}) makes betting unprofitable")
            print(f"• Checking controls pot size with marginal hand")
        elif opponent_action == "CHECK":
            print(f"• Pot control - no need to build pot with medium strength")
        
        if "wet" in board_texture or "coordinated" in board_texture:
            print(f"• {board_texture.title()} board is dangerous for betting")
    
    elif gto_action == "CALL":
        if current_bet > 0:
            pot_odds = current_bet / (pot_size + current_bet)
            print(f"• Equity ({equity:.1%}) exceeds pot odds ({pot_odds:.1%})")
            print(f"• Profitable call based on pot odds")
            
            if equity > pot_odds + 0.1:
                print(f"• Comfortable call with good equity margin")
            else:
                print(f"• Close decision but equity justifies the call")
    
    elif gto_action == "RAISE":
        if equity > 0.7:
            print(f"• Excellent equity ({equity:.1%}) justifies raising for value")
            print(f"• Want to maximize value from strong hand")
        elif equity > 0.5:
            print(f"• Good equity ({equity:.1%}) allows for semi-bluff raise")
        
        if opponent_action in ["BET"]:
            print(f"• Opponent's bet shows strength, but our hand is stronger")
    
    elif gto_action == "FOLD":
        if current_bet > 0:
            pot_odds = current_bet / (pot_size + current_bet)
            print(f"• Equity ({equity:.1%}) is below pot odds ({pot_odds:.1%})")
            print(f"• Unprofitable call - folding saves money")
        
        print(f"• Hand lacks sufficient equity to continue profitably")
        
        if "wet" in board_texture or "coordinated" in board_texture:
            print(f"• {board_texture.title()} board favors opponent's range")
    
    # Position and stage specific advice
    if position in ['EP', 'MP']:
        print(f"• Early/Middle position requires more caution")
    elif position in ['LP']:
        print(f"• Late position allows more aggressive play")
    
    if stage == "river":
        print(f"• River decisions are crucial - no more cards to improve")
    elif stage == "turn":
        print(f"• Turn play sets up river strategy")

def apply_enhanced_explanations():
    """Apply enhanced GTO explanations to the poker trainer"""
    
    # Add the analysis functions to global scope
    globals()['analyze_board_texture'] = analyze_board_texture
    globals()['analyze_hand_vs_board'] = analyze_hand_vs_board
    globals()['explain_preflop_gto_enhanced'] = explain_preflop_gto_enhanced
    globals()['explain_postflop_gto_enhanced'] = explain_postflop_gto_enhanced
    
    print("🎯 Enhanced GTO Explanations Applied!")
    print("✅ Added detailed board texture analysis")
    print("✅ Added hand strength assessment")
    print("✅ Added position-based strategic reasoning")
    print("✅ Added opponent action interpretation")
    print("✅ Added equity vs pot odds explanations")
    print("✅ Added stage-specific advice")
    print("\n📚 Now provides comprehensive explanations for:")
    print("• Why each GTO recommendation is optimal")
    print("• How visible cards affect the decision")
    print("• How opponent actions influence strategy")
    print("• Position-based adjustments")
    print("• Mathematical reasoning (equity vs pot odds)")
    print("• Board texture considerations")

if __name__ == "__main__":
    print("🃏 Enhanced GTO Explanations for Poker Trainer")
    print("=" * 50)
    print("This patch adds detailed explanations of GTO decisions.")
    print("\nTo apply in Jupyter notebook:")
    print("exec(open('enhanced_gto_explanations.py').read())")
    print("apply_enhanced_explanations()")
    print("\nThen the trainer will provide detailed explanations!")
    
    apply_enhanced_explanations()

#!/usr/bin/env python3
"""
Quick patch to fix the GTO recommendation bug in existing poker trainer notebooks.
This can be run in a Jupyter cell to apply the fix without updating the entire notebook.
"""

def apply_gto_fix():
    """Apply the GTO recommendation fix"""
    
    # Fixed GTO function
    def gto_action_recommendation_fixed(equity, position, pot_size, bet_size, stack_size):
        """Enhanced GTO recommendation with position adjustments - FIXED"""
        
        # Define positions if not already defined
        POSITIONS = {
            'SB': {'name': 'Small Blind', 'aggression': 0.3, 'fold_threshold': 0.4},
            'BB': {'name': 'Big Blind', 'aggression': 0.4, 'fold_threshold': 0.35},
            'EP': {'name': 'Early Position', 'aggression': 0.2, 'fold_threshold': 0.5},
            'MP': {'name': 'Middle Position', 'aggression': 0.35, 'fold_threshold': 0.45},
            'LP': {'name': 'Late Position', 'aggression': 0.5, 'fold_threshold': 0.3}
        }
        
        pot_odds = bet_size / (pot_size + bet_size) if bet_size > 0 else 0
        
        # Position-based adjustments
        position_data = POSITIONS.get(position, POSITIONS['MP'])
        aggression_bonus = position_data['aggression'] * 0.1
        
        # FIXED: Handle no bet scenario (opponent checked)
        if bet_size == 0:
            # When opponent checks, we can CHECK or BET
            bet_threshold = 0.55 + aggression_bonus  # Threshold for betting
            if equity > bet_threshold:
                return "BET"  # Strong hand, bet for value
            else:
                return "CHECK"  # Weaker hand, check behind
        else:
            # When facing a bet, we can FOLD, CALL, or RAISE
            if equity > 0.65 + aggression_bonus:
                return "RAISE" if stack_size > bet_size * 3 else "CALL"
            elif equity > pot_odds + 0.05 + aggression_bonus:
                return "CALL"
            else:
                return "FOLD"
    
    # Replace the global function
    globals()['gto_action_recommendation'] = gto_action_recommendation_fixed
    
    print("🔧 GTO Recommendation Fix Applied!")
    print("✅ Fixed: No longer recommends 'CALL' when opponent checks")
    print("✅ Now properly recommends 'BET' or 'CHECK' based on equity")
    print("✅ Maintains correct logic for all other scenarios")
    print("\n🎯 The trainer will now give correct GTO recommendations!")

# Instructions for use
print("🃏 GTO Recommendation Bug Fix")
print("=" * 40)
print("This fixes the bug where 'CALL' was recommended when opponent checked.")
print("\nTo apply the fix, run this in your Jupyter notebook:")
print("```python")
print("exec(open('gto_fix_patch.py').read())")
print("apply_gto_fix()")
print("```")
print("\nOr simply run: apply_gto_fix()")

if __name__ == "__main__":
    apply_gto_fix()
    
    # Test the fix
    print("\n🧪 Testing the fix...")
    
    # Test scenario from the bug report
    equity = 0.679  # 67.9%
    position = 'LP'
    pot_size = 100
    bet_size = 0  # Opponent checked
    stack_size = 1000
    
    recommendation = gto_action_recommendation_fixed(equity, position, pot_size, bet_size, stack_size)
    print(f"Test: {equity:.1%} equity, opponent checked")
    print(f"Recommendation: {recommendation}")
    print(f"Expected: BET")
    print(f"Result: {'✅ PASS' if recommendation == 'BET' else '❌ FAIL'}")

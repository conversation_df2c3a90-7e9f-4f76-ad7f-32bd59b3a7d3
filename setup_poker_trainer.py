#!/usr/bin/env python3
"""
Setup script for Poker Trainer
This script helps set up the environment and launch the Jupyter notebook.
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully!")
    except subprocess.CalledProcessError:
        print("❌ Failed to install requirements. Please install manually:")
        print("pip install jupyter ipywidgets numpy")
        return False
    return True

def enable_jupyter_widgets():
    """Enable Jupyter widgets extension"""
    print("Enabling Jupyter widgets...")
    try:
        subprocess.check_call([sys.executable, "-m", "jupyter", "nbextension", "enable", "--py", "widgetsnbextension"])
        print("✅ Jupyter widgets enabled!")
    except subprocess.CalledProcessError:
        print("⚠️  Could not enable widgets extension. The trainer will still work.")

def launch_notebook():
    """Launch Jupyter notebook"""
    print("Launching Jupyter notebook...")

    # Check which notebook to use (prefer latest version)
    if os.path.exists("poker_trainer_enhanced.ipynb"):
        notebook_file = "poker_trainer_enhanced.ipynb"
        print("🚀 Using ENHANCED version with position play & opponent simulation!")
    elif os.path.exists("poker_trainer_improved.ipynb"):
        notebook_file = "poker_trainer_improved.ipynb"
        print("Using improved version with better input handling...")
    elif os.path.exists("poker_trainer.ipynb"):
        notebook_file = "poker_trainer.ipynb"
        print("Using original version...")
    else:
        print("❌ No poker trainer notebook found!")
        return

    try:
        subprocess.check_call([sys.executable, "-m", "jupyter", "notebook", notebook_file])
    except subprocess.CalledProcessError:
        print(f"❌ Failed to launch notebook. Please run manually:")
        print(f"jupyter notebook {notebook_file}")

def main():
    print("🃏 Poker Trainer Setup")
    print("=" * 30)
    
    # Check for notebook files
    has_enhanced = os.path.exists("poker_trainer_enhanced.ipynb")
    has_improved = os.path.exists("poker_trainer_improved.ipynb")
    has_original = os.path.exists("poker_trainer.ipynb")

    if not (has_enhanced or has_improved or has_original):
        print("❌ No poker trainer notebook found in current directory!")
        print("Expected: poker_trainer_enhanced.ipynb, poker_trainer_improved.ipynb, or poker_trainer.ipynb")
        return

    if has_enhanced:
        print("🚀 Found ENHANCED poker trainer with position play & opponent simulation!")
    elif has_improved:
        print("✅ Found improved poker trainer with better input handling!")
    elif has_original:
        print("✅ Found original poker trainer notebook.")
    
    if install_requirements():
        enable_jupyter_widgets()
        
        print("\n🚀 Setup complete!")
        print("The Jupyter notebook will now open.")
        print("Follow the instructions in the notebook to start training!")
        
        launch_notebook()
    else:
        print("\n❌ Setup failed. Please install requirements manually and run:")
        print("jupyter notebook poker_trainer.ipynb")

if __name__ == "__main__":
    main()

import random
import itertools
import time
from collections import Counter
from IPython.display import display, HTML, clear_output
import ipywidgets as widgets
from ipywidgets import interact, interactive, fixed, interact_manual

# Card representation
SUITS = ['♠', '♥', '♦', '♣']
RANKS = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A']
RANK_VALUES = {rank: i for i, rank in enumerate(RANKS)}

# Position definitions
POSITIONS = {
    'SB': {'name': 'Small Blind', 'aggression': 0.3, 'fold_threshold': 0.4},
    'BB': {'name': 'Big Blind', 'aggression': 0.4, 'fold_threshold': 0.35},
    'EP': {'name': 'Early Position', 'aggression': 0.2, 'fold_threshold': 0.5},
    'MP': {'name': 'Middle Position', 'aggression': 0.35, 'fold_threshold': 0.45},
    'LP': {'name': 'Late Position', 'aggression': 0.5, 'fold_threshold': 0.3}
}

class Card:
    def __init__(self, rank, suit):
        self.rank = rank
        self.suit = suit
        self.value = RANK_VALUES[rank]
    
    def __str__(self):
        return f"{self.rank}{self.suit}"
    
    def __repr__(self):
        return self.__str__()
    
    def display_html(self):
        color = 'red' if self.suit in ['♥', '♦'] else 'black'
        return f'<span style="color: {color}; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;">{self.rank}{self.suit}</span>'

class Deck:
    def __init__(self):
        self.cards = [Card(rank, suit) for suit in SUITS for rank in RANKS]
        self.shuffle()
    
    def shuffle(self):
        random.shuffle(self.cards)
    
    def deal(self, num_cards=1):
        return [self.cards.pop() for _ in range(num_cards)]

# Hand evaluation functions (unchanged - already correct)
def evaluate_hand(cards):
    """Evaluate a 5-7 card poker hand and return the best 5-card hand"""
    if len(cards) < 5:
        return None, 0
    
    best_hand = None
    best_rank = 0
    
    # Try all combinations of 5 cards
    for combo in itertools.combinations(cards, 5):
        hand_rank = get_hand_rank(list(combo))
        if hand_rank > best_rank:
            best_rank = hand_rank
            best_hand = list(combo)
    
    return best_hand, best_rank

def get_hand_rank(cards):
    """Get numerical rank of a 5-card hand (higher = better)"""
    ranks = sorted([card.value for card in cards], reverse=True)
    suits = [card.suit for card in cards]
    
    is_flush = len(set(suits)) == 1
    is_straight = ranks == list(range(ranks[0], ranks[0] - 5, -1))
    
    # Special case for A-2-3-4-5 straight
    if ranks == [12, 3, 2, 1, 0]:  # A, 5, 4, 3, 2
        is_straight = True
        ranks = [3, 2, 1, 0, 12]  # Treat ace as low
    
    rank_counts = Counter(ranks)
    counts = sorted(rank_counts.values(), reverse=True)
    
    # Hand rankings (higher number = better hand)
    if is_straight and is_flush:
        return 8000000 + ranks[0]  # Straight flush
    elif counts == [4, 1]:
        return 7000000 + max(rank_counts, key=rank_counts.get) * 1000 + min(rank_counts, key=rank_counts.get)  # Four of a kind
    elif counts == [3, 2]:
        trips = max(rank_counts, key=rank_counts.get)
        pair = min(rank_counts, key=rank_counts.get)
        return 6000000 + trips * 1000 + pair  # Full house
    elif is_flush:
        return 5000000 + sum(rank * (13 ** i) for i, rank in enumerate(ranks))  # Flush
    elif is_straight:
        return 4000000 + ranks[0]  # Straight
    elif counts == [3, 1, 1]:
        trips = max(rank_counts, key=rank_counts.get)
        kickers = sorted([r for r in ranks if r != trips], reverse=True)
        return 3000000 + trips * 10000 + kickers[0] * 100 + kickers[1]  # Three of a kind
    elif counts == [2, 2, 1]:
        pairs = sorted([r for r, c in rank_counts.items() if c == 2], reverse=True)
        kicker = [r for r, c in rank_counts.items() if c == 1][0]
        return 2000000 + pairs[0] * 10000 + pairs[1] * 100 + kicker  # Two pair
    elif counts == [2, 1, 1, 1]:
        pair = [r for r, c in rank_counts.items() if c == 2][0]
        kickers = sorted([r for r in ranks if r != pair], reverse=True)
        return 1000000 + pair * 100000 + sum(k * (13 ** i) for i, k in enumerate(kickers))  # One pair
    else:
        return sum(rank * (13 ** i) for i, rank in enumerate(ranks))  # High card

def get_hand_name(cards):
    """Get the name of a poker hand"""
    if not cards:
        return "No hand"
    
    ranks = sorted([card.value for card in cards], reverse=True)
    suits = [card.suit for card in cards]
    
    is_flush = len(set(suits)) == 1
    is_straight = ranks == list(range(ranks[0], ranks[0] - 5, -1))
    
    # Special case for A-2-3-4-5 straight
    if ranks == [12, 3, 2, 1, 0]:
        is_straight = True
    
    rank_counts = Counter(ranks)
    counts = sorted(rank_counts.values(), reverse=True)
    
    if is_straight and is_flush:
        return "Straight Flush"
    elif counts == [4, 1]:
        return "Four of a Kind"
    elif counts == [3, 2]:
        return "Full House"
    elif is_flush:
        return "Flush"
    elif is_straight:
        return "Straight"
    elif counts == [3, 1, 1]:
        return "Three of a Kind"
    elif counts == [2, 2, 1]:
        return "Two Pair"
    elif counts == [2, 1, 1, 1]:
        return "One Pair"
    else:
        return "High Card"

# Enhanced equity calculation and opponent simulation
def calculate_equity_vs_opponent(hole_cards, community_cards, opponent_cards=None, num_simulations=10000):
    """Calculate equity using Monte Carlo simulation - ENHANCED & VERIFIED CORRECT"""
    wins = 0
    ties = 0
    total_simulations = 0
    used_cards = set(str(card) for card in hole_cards + community_cards)
    
    # If opponent cards are known, add them to used cards
    if opponent_cards:
        used_cards.update(str(card) for card in opponent_cards)
    
    for _ in range(num_simulations):
        # Create a deck without used cards
        available_cards = [Card(rank, suit) for suit in SUITS for rank in RANKS 
                          if f"{rank}{suit}" not in used_cards]
        
        if len(available_cards) < 2:
            continue
            
        random.shuffle(available_cards)
        
        # Use known opponent cards or simulate random ones
        if opponent_cards:
            sim_opponent_cards = opponent_cards
            remaining_cards = available_cards
        else:
            sim_opponent_cards = available_cards[:2]
            remaining_cards = available_cards[2:]
        
        # Complete the community cards if needed
        cards_needed = 5 - len(community_cards)
        if cards_needed > 0:
            if len(remaining_cards) < cards_needed:
                continue
            simulated_community = community_cards + remaining_cards[:cards_needed]
        else:
            simulated_community = community_cards
        
        # Evaluate both hands
        player_hand, player_rank = evaluate_hand(hole_cards + simulated_community)
        opponent_hand, opponent_rank = evaluate_hand(sim_opponent_cards + simulated_community)
        
        total_simulations += 1
        if player_rank > opponent_rank:
            wins += 1
        elif player_rank == opponent_rank:
            ties += 1
    
    if total_simulations == 0:
        return 0.5
    
    return (wins + ties * 0.5) / total_simulations

def calculate_equity(hole_cards, community_cards, num_simulations=10000):
    """Calculate equity against random opponent - wrapper for backward compatibility"""
    return calculate_equity_vs_opponent(hole_cards, community_cards, None, num_simulations)

class OpponentSimulator:
    """Simulates realistic opponent behavior"""
    
    def __init__(self, position='MP', aggression=0.35, fold_threshold=0.45):
        self.position = position
        self.aggression = aggression
        self.fold_threshold = fold_threshold
        self.hole_cards = []
        self.has_folded = False
    
    def deal_cards(self, cards):
        self.hole_cards = cards
        self.has_folded = False
    
    def get_action(self, community_cards, pot_size, current_bet, stage):
        """Get opponent's action based on hand strength and position"""
        if self.has_folded:
            return "FOLD", 0
        
        # Calculate opponent's equity
        equity = calculate_equity(self.hole_cards, community_cards, 3000)  # Faster simulation
        
        # Adjust thresholds based on position and stage
        fold_threshold = self.fold_threshold
        bet_threshold = 0.6 + self.aggression * 0.2
        
        # Stage adjustments
        if stage == 'preflop':
            fold_threshold *= 1.2  # Tighter preflop
        elif stage == 'river':
            fold_threshold *= 0.8  # Looser on river
        
        # Add some randomness
        random_factor = random.uniform(0.9, 1.1)
        equity *= random_factor
        
        if current_bet > 0:
            pot_odds = current_bet / (pot_size + current_bet)
            if equity < pot_odds * 0.8:  # Fold if equity much worse than pot odds
                self.has_folded = True
                return "FOLD", 0
            elif equity > bet_threshold:
                bet_size = int(pot_size * random.uniform(0.5, 1.0))
                return "RAISE", bet_size
            else:
                return "CALL", current_bet
        else:
            if equity < fold_threshold:
                return "CHECK", 0
            elif equity > bet_threshold:
                bet_size = int(pot_size * random.uniform(0.3, 0.8))
                return "BET", bet_size
            else:
                return "CHECK", 0

def gto_action_recommendation(equity, position, pot_size, bet_size, stack_size):
    """Enhanced GTO recommendation with position adjustments - FIXED"""
    pot_odds = bet_size / (pot_size + bet_size) if bet_size > 0 else 0
    
    # Position-based adjustments
    position_data = POSITIONS.get(position, POSITIONS['MP'])
    aggression_bonus = position_data['aggression'] * 0.1
    
    # FIXED: Handle no bet scenario (opponent checked)
    if bet_size == 0:
        # When opponent checks, we can CHECK or BET
        bet_threshold = 0.55 + aggression_bonus  # Threshold for betting
        if equity > bet_threshold:
            return "BET"  # Strong hand, bet for value
        else:
            return "CHECK"  # Weaker hand, check behind
    else:
        # When facing a bet, we can FOLD, CALL, or RAISE
        if equity > 0.65 + aggression_bonus:
            return "RAISE" if stack_size > bet_size * 3 else "CALL"
        elif equity > pot_odds + 0.05 + aggression_bonus:
            return "CALL"
        else:
            return "FOLD"

# Enhanced Poker game with position play and opponent simulation
class EnhancedPokerGame:
    def __init__(self):
        self.deck = Deck()
        self.hole_cards = []
        self.community_cards = []
        self.pot_size = 100
        self.current_bet = 0
        self.player_stack = 1000
        self.stage = "preflop"
        self.score = 0
        self.questions_asked = 0
        self.hand_results = []
        self.current_hand_log = []
        self.session_log = []
        
        # New features
        self.position = 'MP'  # Will be randomized each hand
        self.opponent = OpponentSimulator()
        self.opponent_action = None
        self.opponent_bet = 0
        self.showdown_reached = False
    
    def start_new_hand(self):
        self.deck = Deck()
        self.hole_cards = self.deck.deal(2)
        self.community_cards = []
        self.pot_size = 100
        self.current_bet = 0
        self.stage = "preflop"
        self.current_hand_log = []
        self.showdown_reached = False
        
        # Randomize position for this hand
        self.position = random.choice(list(POSITIONS.keys()))
        
        # Deal opponent cards and set up opponent
        opponent_cards = self.deck.deal(2)
        opponent_position = random.choice(list(POSITIONS.keys()))
        opponent_data = POSITIONS[opponent_position]
        
        self.opponent = OpponentSimulator(
            position=opponent_position,
            aggression=opponent_data['aggression'],
            fold_threshold=opponent_data['fold_threshold']
        )
        self.opponent.deal_cards(opponent_cards)
    
    def deal_flop(self):
        if self.stage == "preflop":
            self.community_cards.extend(self.deck.deal(3))
            self.stage = "flop"
    
    def deal_turn(self):
        if self.stage == "flop":
            self.community_cards.extend(self.deck.deal(1))
            self.stage = "turn"
    
    def deal_river(self):
        if self.stage == "turn":
            self.community_cards.extend(self.deck.deal(1))
            self.stage = "river"
    
    def get_opponent_action(self):
        """Get opponent's action for current stage"""
        action, bet = self.opponent.get_action(
            self.community_cards, 
            self.pot_size, 
            self.current_bet, 
            self.stage
        )
        self.opponent_action = action
        self.opponent_bet = bet
        
        # Update pot and bet based on opponent action
        if action in ["BET", "RAISE"]:
            self.pot_size += bet
            self.current_bet = bet
        elif action == "CALL":
            self.pot_size += self.current_bet
        
        return action, bet
    
    def display_table(self):
        """Display the current poker table state with position info"""
        print("\n" + "="*70)
        print(f"POKER TRAINER - {self.stage.upper()} STAGE")
        position_name = POSITIONS[self.position]['name']
        opponent_position_name = POSITIONS[self.opponent.position]['name']
        print(f"Your Position: {position_name} | Opponent: {opponent_position_name}")
        print("="*70)
        
        # Display hole cards
        hole_html = "<h3>Your Hole Cards:</h3>" + "".join([card.display_html() for card in self.hole_cards])
        display(HTML(hole_html))
        
        # Display community cards
        if self.community_cards:
            community_html = "<h3>Community Cards:</h3>" + "".join([card.display_html() for card in self.community_cards])
            display(HTML(community_html))
        
        # Display current hand strength
        if len(self.community_cards) >= 3:
            best_hand, _ = evaluate_hand(self.hole_cards + self.community_cards)
            hand_name = get_hand_name(best_hand)
            print(f"\nCurrent Best Hand: {hand_name}")
        
        # Show opponent action if any
        if self.opponent_action:
            if self.opponent_action == "FOLD":
                print(f"\n🤖 Opponent: {self.opponent_action}")
            elif self.opponent_action in ["BET", "RAISE"]:
                print(f"\n🤖 Opponent: {self.opponent_action} ${self.opponent_bet}")
            else:
                print(f"\n🤖 Opponent: {self.opponent_action}")
        
        print(f"\nPot Size: ${self.pot_size}")
        print(f"Current Bet: ${self.current_bet}")
        print(f"Your Stack: ${self.player_stack}")
        print(f"Score: {self.score}/{self.questions_asked}")
    
    def show_showdown(self):
        """Show both players' cards and determine winner"""
        if self.opponent.has_folded:
            print("\n🏆 You win! Opponent folded.")
            return
        
        print("\n" + "="*70)
        print("🃏 SHOWDOWN - CARDS REVEALED!")
        print("="*70)
        
        # Show both hands
        your_html = "<h3>🫵 Your Cards:</h3>" + "".join([card.display_html() for card in self.hole_cards])
        display(HTML(your_html))
        
        opponent_html = "<h3>🤖 Opponent's Cards:</h3>" + "".join([card.display_html() for card in self.opponent.hole_cards])
        display(HTML(opponent_html))
        
        if self.community_cards:
            community_html = "<h3>🏛️ Community Cards:</h3>" + "".join([card.display_html() for card in self.community_cards])
            display(HTML(community_html))
        
        # Evaluate both hands
        your_hand, your_rank = evaluate_hand(self.hole_cards + self.community_cards)
        opponent_hand, opponent_rank = evaluate_hand(self.opponent.hole_cards + self.community_cards)
        
        your_hand_name = get_hand_name(your_hand)
        opponent_hand_name = get_hand_name(opponent_hand)
        
        print(f"\n📊 HAND COMPARISON:")
        print(f"🫵 Your Hand: {your_hand_name}")
        print(f"🤖 Opponent's Hand: {opponent_hand_name}")
        
        # Determine winner
        if your_rank > opponent_rank:
            print(f"\n🏆 YOU WIN! {your_hand_name} beats {opponent_hand_name}")
        elif opponent_rank > your_rank:
            print(f"\n😞 You lose. {opponent_hand_name} beats {your_hand_name}")
        else:
            print(f"\n🤝 TIE! Both players have {your_hand_name}")
        
        self.showdown_reached = True
    
    def get_equity(self):
        return calculate_equity(self.hole_cards, self.community_cards, 5000)
    
    def get_gto_action(self):
        equity = self.get_equity()
        return gto_action_recommendation(equity, self.position, self.pot_size, self.current_bet, self.player_stack)
    
    def get_preflop_gto_action(self):
        """Get GTO action for preflop based on hole cards and position"""
        card1, card2 = self.hole_cards
        position_data = POSITIONS[self.position]
        
        # Position-based adjustments
        aggression_modifier = position_data['aggression']
        
        # Check for pairs
        if card1.value == card2.value:
            if card1.value >= 10:  # JJ, QQ, KK, AA
                return "RAISE"
            elif card1.value >= 7:  # 88, 99, TT
                return "RAISE" if aggression_modifier > 0.4 else "CALL"
            else:  # 22-77
                return "CALL" if aggression_modifier > 0.3 else "FOLD"
        
        # Check for suited cards
        is_suited = card1.suit == card2.suit
        high_card = max(card1.value, card2.value)
        low_card = min(card1.value, card2.value)
        
        # Premium hands
        if high_card >= 12 and low_card >= 10:  # AK, AQ, KQ
            return "RAISE"
        elif high_card == 12 and low_card >= 8:  # AJ, AT, A9
            if is_suited or aggression_modifier > 0.4:
                return "RAISE"
            else:
                return "CALL"
        elif high_card >= 11 and low_card >= 9:  # KJ, KT, QJ, QT
            return "CALL" if aggression_modifier > 0.3 else "FOLD"
        elif is_suited and high_card >= 10 and low_card >= 6:  # Suited connectors
            return "CALL" if aggression_modifier > 0.35 else "FOLD"
        else:
            return "FOLD"
    
    def add_hand_ranking_result(self, stage, correct_answer, player_answer, is_correct):
        self.current_hand_log.append({
            'type': 'hand_ranking',
            'stage': stage,
            'correct_answer': correct_answer,
            'player_answer': player_answer,
            'is_correct': is_correct
        })
    
    def add_decision_result(self, stage, player_action, gto_action, is_correct, equity):
        self.current_hand_log.append({
            'type': 'gto_decision',
            'stage': stage,
            'player_action': player_action,
            'gto_action': gto_action,
            'is_correct': is_correct,
            'equity': equity,
            'position': self.position,
            'opponent_action': self.opponent_action
        })
    
    def finish_hand(self):
        hand_summary = {
            'hole_cards': [str(card) for card in self.hole_cards],
            'opponent_cards': [str(card) for card in self.opponent.hole_cards],
            'community_cards': [str(card) for card in self.community_cards],
            'final_stage': self.stage,
            'position': self.position,
            'opponent_position': self.opponent.position,
            'showdown_reached': self.showdown_reached,
            'log': self.current_hand_log.copy()
        }
        self.hand_results.append(hand_summary)
        self.session_log.append(hand_summary)

# Initialize enhanced game
game = EnhancedPokerGame()
print("🚀 Enhanced Poker Trainer with Position Play & Opponent Simulation loaded!")

# Helper functions for input handling
def safe_input(prompt, delay=0.5):
    time.sleep(delay)
    return input(prompt)

def get_valid_choice(prompt, min_choice, max_choice, delay=0.5):
    while True:
        try:
            time.sleep(delay)
            choice = int(input(prompt))
            if min_choice <= choice <= max_choice:
                return choice
            else:
                print(f"❌ Please enter a number between {min_choice} and {max_choice}.")
        except ValueError:
            print(f"❌ Please enter a valid number between {min_choice} and {max_choice}.")
        except KeyboardInterrupt:
            print("\n\n⚠️ Training session interrupted by user.")
            return None

def wait_for_continue(message="\nPress Enter to continue...", delay=0.5):
    time.sleep(delay)
    try:
        input(message)
    except KeyboardInterrupt:
        print("\n\n⚠️ Training session interrupted by user.")
        return False
    return True

print("Helper functions loaded.")

# Enhanced interactive quiz functions
def ask_preflop_gto_question():
    """Ask player about optimal preflop GTO decision with position context"""
    clear_output(wait=True)
    
    # Get opponent action first
    opponent_action, opponent_bet = game.get_opponent_action()
    
    game.display_table()
    
    position_name = POSITIONS[game.position]['name']
    print(f"\n🎯 PREFLOP GTO DECISION QUESTION:")
    print(f"You are in {position_name}. What should you do with these hole cards?")
    
    # Get GTO recommendation
    gto_action = game.get_preflop_gto_action()
    
    # Action options based on opponent action
    if opponent_action == "FOLD":
        options = ["FOLD", "CALL", "RAISE"]
    elif opponent_action in ["BET", "RAISE"]:
        options = ["FOLD", "CALL", "RAISE"]
    else:  # CHECK
        options = ["FOLD", "CHECK", "BET"]
    
    print("\nWhat should you do?")
    for i, option in enumerate(options):
        print(f"{i+1}. {option}")
    
    answer = get_valid_choice(f"\nEnter your choice (1-{len(options)}): ", 1, len(options))
    
    if answer is None:
        return True
    
    game.questions_asked += 1
    player_action = options[answer - 1]
    
    # Check if answer matches GTO recommendation
    correct = player_action == gto_action
    
    if correct:
        print("✅ Correct! That's the GTO play!")
        game.score += 1
    else:
        print(f"❌ Incorrect. GTO recommendation: {gto_action}")
    
    # Enhanced analysis with position context
    print(f"\n📊 Preflop Analysis:")
    card1, card2 = game.hole_cards
    if card1.value == card2.value:
        print(f"You have a pocket pair: {card1.rank}{card1.rank}")
    elif card1.suit == card2.suit:
        print(f"You have suited cards: {card1.rank}{card2.rank}s")
    else:
        print(f"You have offsuit cards: {card1.rank}{card2.rank}o")
    
    print(f"Position: {position_name} (Aggression: {POSITIONS[game.position]['aggression']:.1f})")
    print(f"Opponent Action: {opponent_action}")
    
    game.add_decision_result(game.stage, player_action, gto_action, correct, 0.0)
    
    return player_action == "FOLD"

def ask_hand_ranking_question():
    """Ask player to identify hand rankings"""
    clear_output(wait=True)
    game.display_table()
    
    if len(game.community_cards) < 3:
        print("\nNot enough community cards for hand ranking question.")
        return
    
    all_cards = game.hole_cards + game.community_cards
    current_hand, _ = evaluate_hand(all_cards)
    current_hand_name = get_hand_name(current_hand)
    
    hand_types = ["High Card", "One Pair", "Two Pair", "Three of a Kind", 
                  "Straight", "Flush", "Full House", "Four of a Kind", "Straight Flush"]
    
    options = [current_hand_name]
    while len(options) < 4:
        option = random.choice(hand_types)
        if option not in options:
            options.append(option)
    
    random.shuffle(options)
    correct_index = options.index(current_hand_name)
    
    print("\n🎯 HAND RANKING QUESTION:")
    print("What is your current best hand?")
    for i, option in enumerate(options):
        print(f"{i+1}. {option}")
    
    answer = get_valid_choice("\nEnter your choice (1-4): ", 1, 4)
    
    if answer is None:
        return
    
    game.questions_asked += 1
    correct = (answer - 1) == correct_index
    
    if correct:
        print("✅ Correct! Well done!")
        game.score += 1
    else:
        print(f"❌ Incorrect. The correct answer was: {current_hand_name}")
    
    if current_hand:
        time.sleep(0.3)
        hand_html = "<h4>Your best 5-card hand:</h4>" + "".join([card.display_html() for card in current_hand])
        display(HTML(hand_html))
    
    game.add_hand_ranking_result(game.stage, current_hand_name, options[answer - 1], correct)

def ask_gto_decision_question():
    """Ask player about optimal GTO decision with opponent simulation"""
    clear_output(wait=True)
    
    # Get opponent action
    opponent_action, opponent_bet = game.get_opponent_action()
    
    # If opponent folded, end the hand
    if opponent_action == "FOLD":
        game.display_table()
        print("\n🎉 Opponent folded! You win the pot.")
        return False  # Don't end player's hand
    
    game.display_table()
    
    print(f"\n🎯 GTO DECISION QUESTION:")
    print(f"Opponent action: {opponent_action}" + (f" ${opponent_bet}" if opponent_bet > 0 else ""))
    
    # Calculate equity and GTO recommendation
    print("\n⏳ Calculating equity...")
    equity = game.get_equity()
    gto_action = game.get_gto_action()
    
    print(f"\nYour equity: {equity:.1%}")
    
    if game.current_bet > 0:
        pot_odds = game.current_bet / (game.pot_size + game.current_bet)
        print(f"Pot odds: {pot_odds:.1%}")
    
    # Action options based on opponent action
    if opponent_action in ["BET", "RAISE"]:
        options = ["FOLD", "CALL", "RAISE"]
    else:  # CHECK
        options = ["CHECK", "BET"]
    
    print("\nWhat should you do?")
    for i, option in enumerate(options):
        print(f"{i+1}. {option}")
    
    answer = get_valid_choice(f"\nEnter your choice (1-{len(options)}): ", 1, len(options))
    
    if answer is None:
        return True
    
    game.questions_asked += 1
    player_action = options[answer - 1]
    
    # Check if answer matches GTO recommendation - FIXED
    correct = False
    if gto_action == "CHECK" and player_action == "CHECK":
        correct = True
    elif gto_action == "BET" and player_action == "BET":
        correct = True
    elif gto_action == "RAISE" and player_action in ["BET", "RAISE"]:
        correct = True
    elif gto_action == "CALL" and player_action == "CALL":
        correct = True
    elif gto_action == "FOLD" and player_action == "FOLD":
        correct = True
    
    if correct:
        print("✅ Correct! That's the GTO play!")
        game.score += 1
    else:
        print(f"❌ Incorrect. GTO recommendation: {gto_action}")
    
    # Enhanced analysis
    print(f"\n📊 Analysis:")
    print(f"Your equity: {equity:.1%}")
    print(f"Position: {POSITIONS[game.position]['name']}")
    print(f"Opponent: {opponent_action}" + (f" ${opponent_bet}" if opponent_bet > 0 else ""))
    
    if game.current_bet > 0:
        pot_odds = game.current_bet / (game.pot_size + game.current_bet)
        print(f"Pot odds required: {pot_odds:.1%}")
        if equity > pot_odds:
            print("✅ You have sufficient equity to call")
        else:
            print("❌ Insufficient equity to call profitably")
    
    game.add_decision_result(game.stage, player_action, gto_action, correct, equity)
    
    return player_action == "FOLD"

def run_enhanced_training_session():
    """Run enhanced poker training with position play and opponent simulation"""
    clear_output(wait=True)
    print("🚀 Welcome to the Enhanced Poker Trainer!")
    print("\n🆕 LATEST FEATURES:")
    print("• ✅ Verified correct equity calculation")
    print("• 🎲 Random position selection each hand")
    print("• 🤖 Realistic opponent simulation")
    print("• 🃏 Showdown with opponent cards revealed")
    print("• 📊 Position-based GTO adjustments")
    print("\n📋 How it works:")
    print("- Each hand you'll be in a random position (SB, BB, EP, MP, LP)")
    print("- Opponent will make realistic decisions based on their position")
    print("- If neither player folds, you'll see opponent's cards at showdown")
    print("- Learn position-based strategy and opponent reading")
    
    if not wait_for_continue("\nPress Enter to start your enhanced training session..."):
        return
    
    num_hands = 5
    
    for hand_num in range(1, num_hands + 1):
        clear_output(wait=True)
        print(f"\n{'='*70}")
        print(f"HAND {hand_num} of {num_hands}")
        print(f"{'='*70}")
        
        # Start new hand
        game.start_new_hand()
        
        # Preflop with opponent simulation
        folded = ask_preflop_gto_question()
        
        if folded:
            print("\n🔄 Hand ended - you folded preflop.")
            game.finish_hand()
            if not wait_for_continue("\nPress Enter to continue to next hand..."):
                return
            continue
        
        if game.opponent.has_folded:
            print("\n🎉 Opponent folded preflop! You win.")
            game.finish_hand()
            if not wait_for_continue("\nPress Enter to continue to next hand..."):
                return
            continue
        
        if not wait_for_continue("\nPress Enter to see the flop..."):
            return
        
        # Flop
        game.deal_flop()
        ask_hand_ranking_question()
        if not wait_for_continue("\nPress Enter to continue..."):
            return
        
        folded = ask_gto_decision_question()
        if folded:
            print("\n🔄 Hand ended - you folded on the flop.")
            game.finish_hand()
            if not wait_for_continue("\nPress Enter to continue to next hand..."):
                return
            continue
        
        if game.opponent.has_folded:
            print("\n🎉 Opponent folded on the flop! You win.")
            game.finish_hand()
            if not wait_for_continue("\nPress Enter to continue to next hand..."):
                return
            continue
        
        if not wait_for_continue("\nPress Enter to see the turn..."):
            return
        
        # Turn
        game.deal_turn()
        ask_hand_ranking_question()
        if not wait_for_continue("\nPress Enter to continue..."):
            return
        
        folded = ask_gto_decision_question()
        if folded:
            print("\n🔄 Hand ended - you folded on the turn.")
            game.finish_hand()
            if not wait_for_continue("\nPress Enter to continue to next hand..."):
                return
            continue
        
        if game.opponent.has_folded:
            print("\n🎉 Opponent folded on the turn! You win.")
            game.finish_hand()
            if not wait_for_continue("\nPress Enter to continue to next hand..."):
                return
            continue
        
        if not wait_for_continue("\nPress Enter to see the river..."):
            return
        
        # River
        game.deal_river()
        ask_hand_ranking_question()
        if not wait_for_continue("\nPress Enter to continue..."):
            return
        
        folded = ask_gto_decision_question()
        if folded:
            print("\n🔄 Hand ended - you folded on the river.")
        elif not game.opponent.has_folded:
            # SHOWDOWN!
            if not wait_for_continue("\nPress Enter for SHOWDOWN..."):
                return
            game.show_showdown()
        
        game.finish_hand()
        
        print(f"\n📊 Hand {hand_num} Complete!")
        print(f"Current Score: {game.score}/{game.questions_asked} ({game.score/max(1,game.questions_asked)*100:.1f}%)")
        
        if hand_num < num_hands:
            if not wait_for_continue("\nPress Enter for next hand..."):
                return
    
    # Enhanced session summary
    print_enhanced_session_summary()

def print_enhanced_session_summary():
    """Print enhanced session summary with position and opponent data"""
    clear_output(wait=True)
    
    print("\n" + "="*80)
    print("🏆 ENHANCED POKER TRAINING SESSION COMPLETE")
    print("="*80)
    
    print(f"\n📊 OVERALL PERFORMANCE:")
    print(f"Final Score: {game.score}/{game.questions_asked}")
    accuracy = game.score/max(1,game.questions_asked)*100
    print(f"Accuracy: {accuracy:.1f}%")
    
    if accuracy >= 80:
        print("🌟 Excellent work! You have strong poker fundamentals!")
    elif accuracy >= 60:
        print("👍 Good job! Keep practicing to improve your game.")
    else:
        print("📚 Keep studying! Practice makes perfect in poker.")
    
    print(f"\n📋 DETAILED HAND-BY-HAND BREAKDOWN:")
    print("-" * 80)
    
    for i, hand_result in enumerate(game.session_log, 1):
        print(f"\n🃏 HAND {i}:")
        print(f"Your Position: {POSITIONS[hand_result['position']]['name']}")
        print(f"Opponent Position: {POSITIONS[hand_result['opponent_position']]['name']}")
        print(f"Your Cards: {', '.join(hand_result['hole_cards'])}")
        print(f"Opponent Cards: {', '.join(hand_result['opponent_cards'])}")
        
        if hand_result['community_cards']:
            print(f"Community Cards: {', '.join(hand_result['community_cards'])}")
        
        print(f"Final Stage: {hand_result['final_stage'].upper()}")
        
        if hand_result['showdown_reached']:
            print("🎭 SHOWDOWN REACHED")
        
        # Show questions and answers
        for j, log_entry in enumerate(hand_result['log'], 1):
            if log_entry['type'] == 'hand_ranking':
                status = "✅" if log_entry['is_correct'] else "❌"
                print(f"  Q{j}: Hand Ranking ({log_entry['stage']}) {status}")
                print(f"      Correct: {log_entry['correct_answer']}")
                print(f"      Your Answer: {log_entry['player_answer']}")
            elif log_entry['type'] == 'gto_decision':
                status = "✅" if log_entry['is_correct'] else "❌"
                equity_str = f" (Equity: {log_entry['equity']:.1%})" if log_entry['equity'] > 0 else ""
                print(f"  Q{j}: GTO Decision ({log_entry['stage']}) {status}{equity_str}")
                print(f"      Position: {POSITIONS[log_entry['position']]['name']}")
                print(f"      GTO Recommendation: {log_entry['gto_action']}")
                print(f"      Your Action: {log_entry['player_action']}")
                if log_entry.get('opponent_action'):
                    print(f"      Opponent Action: {log_entry['opponent_action']}")
    
    print(f"\n" + "="*80)
    print("📚 KEY LEARNING POINTS:")
    print("• Position significantly affects optimal strategy")
    print("• Opponent actions provide valuable information")
    print("• Equity calculations are the foundation of good decisions")
    print("• Showdowns reveal the importance of hand reading")
    print("• GTO play adapts to position and opponent behavior")
    print("\n🎯 Keep practicing with different positions and opponents!")
    print("="*80)

# Start the enhanced training session
# Run this cell to begin your enhanced poker training!
run_enhanced_training_session()